import { useState, useEffect } from 'react';
import { SingleGuestWarningModal } from '../../../components/modals/SingleGuestWarningModal';
import { useGuestList } from '../../../lib/contexts/GuestListContext';

interface Guest {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface SendEmailInviteProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
  onGuestsChange?: (guests: Guest[], source?: 'email') => void;
}

export const SendEmailInvite = ({
  onNextStep,
  onFormActiveChange,
  onGuestsChange,
}: SendEmailInviteProps) => {
  const {
    guests: contextGuests,
    setGuests: setContextGuests,
    guestSource,
  } = useGuestList();
  const [emails, setEmails] = useState<string[]>(
    guestSource === 'email'
      ? contextGuests.map((guest) => guest.email).filter((email) => email)
      : []
  );
  const [inputValue, setInputValue] = useState('');
  const [emailError, setEmailError] = useState('');
  const [showSingleGuestWarningModal, setShowSingleGuestWarningModal] =
    useState(false);

  // Sync local state with context when context changes (e.g., when guests are deleted from preview)
  useEffect(() => {
    if (guestSource === 'email') {
      const contextEmails = contextGuests
        .map((guest) => guest.email)
        .filter((email) => email);
      setEmails(contextEmails);
    }
  }, [contextGuests, guestSource]);

  useEffect(() => {
    onFormActiveChange?.(inputValue !== '' || emails.length > 0);
  }, [inputValue, emails.length, onFormActiveChange]);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      const email = inputValue.trim();
      setEmailError('');

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }

      setEmails([...emails, email]);
      setInputValue('');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (emailError) {
      setEmailError('');
    }
  };

  const handleAddToQueue = () => {
    if (inputValue.trim()) {
      const email = inputValue.trim();
      setEmailError('');

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }

      setEmails([...emails, email]);
      setInputValue('');
    }
  };

  const removeEmail = (index: number) => {
    const updatedEmails = emails.filter((_, i) => i !== index);
    setEmails(updatedEmails);
  };

  const handleInviteGuests = () => {
    const hasQueuedEmails = emails.length > 0;
    const hasInputData = inputValue.trim() !== '';
    if (hasQueuedEmails && !hasInputData) {
      if (emails.length === 1) {
        setShowSingleGuestWarningModal(true);
        return;
      }
      const emailGuests: Guest[] = emails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '',
        lastName: '',
        email: email,
        phone: '',
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');

      if (onNextStep) {
        onNextStep();
      }
      return;
    }
    if (hasInputData) {
      setEmailError('');
      const email = inputValue.trim();

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      if (emails.includes(email)) {
        setEmailError('This email has already been added');
        return;
      }
      if (hasQueuedEmails) {
        const updatedEmails = [...emails, email];
        if (updatedEmails.length === 1) {
          setShowSingleGuestWarningModal(true);
          return;
        }

        const emailGuests: Guest[] = updatedEmails.map((email, index) => ({
          id: Date.now() + index,
          firstName: '',
          lastName: '',
          email: email,
          phone: '',
        }));
        setContextGuests(emailGuests, 'email');
        onGuestsChange?.(emailGuests, 'email');
        setInputValue('');
        setEmailError('');
        if (onNextStep) {
          onNextStep();
        }
        return;
      }
      if (!hasQueuedEmails) {
        setShowSingleGuestWarningModal(true);
        return;
      }
    }
    if (!hasInputData && !hasQueuedEmails) {
      setEmailError('Please add at least one email address');
      return;
    }
  };

  const handleSingleGuestWarningClose = () => {
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningAddToQueue = () => {
    if (
      inputValue.trim() &&
      validateEmail(inputValue.trim()) &&
      !emails.includes(inputValue.trim())
    ) {
      handleAddToQueue();
    }
    setShowSingleGuestWarningModal(false);
  };

  const handleSingleGuestWarningContinue = () => {
    setShowSingleGuestWarningModal(false);
    const hasQueuedEmails = emails.length > 0;
    const hasInputData =
      inputValue.trim() !== '' && validateEmail(inputValue.trim());

    if (hasInputData && !hasQueuedEmails) {
      const emailGuests: Guest[] = [
        {
          id: Date.now(),
          firstName: '',
          lastName: '',
          email: inputValue.trim(),
          phone: '',
        },
      ];
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
    } else if (hasQueuedEmails && hasInputData) {
      const updatedEmails = [...emails, inputValue.trim()];
      const emailGuests: Guest[] = updatedEmails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '',
        lastName: '',
        email: email,
        phone: '',
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
      setInputValue('');
      setEmailError('');
    } else if (hasQueuedEmails && !hasInputData) {
      const emailGuests: Guest[] = emails.map((email, index) => ({
        id: Date.now() + index,
        firstName: '',
        lastName: '',
        email: email,
        phone: '',
      }));
      setContextGuests(emailGuests, 'email');
      onGuestsChange?.(emailGuests, 'email');
    }

    if (onNextStep) {
      onNextStep();
    }
  };

  return (
    <div className="flex-1  pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium ">
        invite guest via email
      </h3>
      <p className="md:text-base text-sm text-grey-250 mb-6">
        Got emails for your guests? Add them and send invites
      </p>

      <div className="">
        <div>
          <label className="block text-sm text-grey-500 font-medium mb-1.5">
            Email
          </label>
          <div className="">
            <input
              type="email"
              placeholder="Enter your guest's email"
              className={` pr-3.5 py-2.5 pl-2.5 w-full rounded-full text-base text-grey-300 outline-none border ${
                emailError ? 'border-red-500' : 'border-grey-200'
              }`}
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
            />
          </div>
          {emailError && (
            <p className="text-red-500 text-xs mt-1 ml-2">{emailError}</p>
          )}
        </div>
        <button
          onClick={handleAddToQueue}
          disabled={!inputValue.trim()}
          className="bg-primary-250 mt-3 hover:bg-primary-250/50 text-primary  text-sm cursor-pointer font-medium py-1 px-3 rounded-full flex items-center mb-2.5">
          <span className="bg-primary text-white text-xs  rounded-full h-3 w-3 flex items-center justify-center mr-2">
            +
          </span>
          Add to Queue
        </button>
        <div className="flex flex-wrap gap-2 mt-5">
          {emails.map((email, index) => (
            <div
              key={index}
              className="bg-gray-100 px-3 py-1 rounded-full flex items-center gap-2">
              <span className="text-grey-500 text-xs md:text-sm font-medium">
                {email}
              </span>
              <button
                onClick={() => removeEmail(index)}
                className="text-grey-500 cursor-pointer">
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-18 py-3.5 border-t border-grey-850 flex justify-end ">
        <button
          onClick={handleInviteGuests}
          className="text-base font-semibold mr-5 text-white h-12 max-w-[135px] w-full rounded-full transition-colors bg-primary cursor-pointer hover:bg-primary/80">
          Invite Guests
        </button>
      </div>
      <SingleGuestWarningModal
        isOpen={showSingleGuestWarningModal}
        onClose={handleSingleGuestWarningClose}
        onAddToQueue={handleSingleGuestWarningAddToQueue}
        onContinue={handleSingleGuestWarningContinue}
      />
    </div>
  );
};
